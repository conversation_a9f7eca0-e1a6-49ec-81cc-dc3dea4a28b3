import { mapGetters } from 'vuex';
import { pdfParseStatus } from '@/store/constants';

// 文件预处理状态 - 需要禁用编辑的状态（解析中、预测中、审核中）
const FILE_EDIT_DISABLED_STATUSES = [
  pdfParseStatus.parsing, // 解析中
  pdfParseStatus.parsing_time, // 解析中
  pdfParseStatus.parsed, // 解析中
];

export default {
  props: {
    scenarioOptions: {
      type: Array,
      default: () => [],
    },
    fileData: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      baseForm: {
        process_methods: [],
        audit_methods: [],
        scenario_id: '',
        molds: [],
      },
    };
  },

  computed: {
    ...mapGetters(['configuration']),
    ...mapGetters('schemaModule', ['schemas']),

    isSchemaDisable() {
      return !this.form.process_methods.includes('extract');
    },

    isAuditMethodDisabled() {
      return (
        this.form.process_methods.length === 1 &&
        this.form.process_methods.includes('extract')
      );
    },

    isRuleAuditDisabled() {
      return (
        this.form.process_methods.length === 1 &&
        this.form.process_methods.includes('intelligent')
      );
    },

    isScenarioDisabled() {
      return (
        this.form.audit_methods.length === 1 &&
        this.form.audit_methods.includes('rule')
      );
    },

    // 检查文件是否处于需要禁用编辑的状态
    isFileInDisabledStatus() {
      if (!this.fileData) return false;

      const { pdf_parse_status } = this.fileData;
      return FILE_EDIT_DISABLED_STATUSES.includes(pdf_parse_status);
    },

    // 处理方式选择器是否禁用
    isProcessMethodsDisabled() {
      return this.isFileInDisabledStatus;
    },

    // 审核方式选择器是否禁用
    isAuditMethodsDisabled() {
      return this.isFileInDisabledStatus || this.isAuditMethodDisabled;
    },

    // Schema配置是否禁用
    isSchemaConfigDisabled() {
      return this.isFileInDisabledStatus || this.isSchemaDisable;
    },

    // 应用场景选择是否禁用
    isScenarioConfigDisabled() {
      return (
        this.isFileInDisabledStatus ||
        this.isAuditMethodDisabled ||
        this.isScenarioDisabled
      );
    },

    baseFormRules() {
      const self = this;
      return {
        process_methods: [
          {
            required: true,
            type: 'array',
            min: 1,
            message: '请选择处理方式',
            trigger: ['change', 'blur'],
          },
        ],
        audit_methods: [
          {
            validator(rule, value, callback) {
              return self.validateAuditMethods(rule, value, callback);
            },
            trigger: ['change', 'blur'],
          },
        ],
        scenario_id: [
          {
            validator(rule, value, callback) {
              return self.validateScenarioId(rule, value, callback);
            },
            trigger: ['change', 'blur'],
          },
        ],
      };
    },
  },

  methods: {
    validateAuditMethods(_rule, value, callback) {
      if (
        this.form.process_methods.length === 1 &&
        this.form.process_methods.includes('extract')
      ) {
        callback();
        return;
      }
      if (!value || value.length === 0) {
        callback(new Error('请选择审核方式'));
      } else {
        callback();
      }
    },

    validateScenarioId(_rule, value, callback) {
      if (
        this.form.process_methods.length === 1 &&
        this.form.process_methods.includes('extract')
      ) {
        callback();
        return;
      }
      if (
        this.form.audit_methods.length === 1 &&
        this.form.audit_methods.includes('rule')
      ) {
        callback();
        return;
      }
      if (!value || value.length === 0) {
        callback(new Error('请选择应用场景'));
      } else {
        callback();
      }
    },

    getSelectOptionLabel(option) {
      if (!option.marked) {
        return option.name;
      }
      return `${option.name} (已标注)`;
    },

    handleProcessMethodChange(value) {
      if (!value.length) {
        this.form.audit_methods = [];
        this.form.molds = [];
      } else if (value.length === 1 && value.includes('extract')) {
        this.form.audit_methods = [];
        this.form.scenario_id = [];
      } else if (value.length === 1 && value.includes('intelligent')) {
        this.form.audit_methods = ['judge'];
        this.form.molds = [];
      }

      if (this.$refs.form) {
        this.$refs.form.clearValidate();
        this.$nextTick(() => {
          this.$refs.form.validateField(['audit_methods', 'scenario_id']);
        });
      }
    },

    handleAuditMethodChange(value) {
      if (value.length === 1 && value.includes('rule')) {
        this.form.scenario_id = [];
      }

      if (this.$refs.form) {
        this.$nextTick(() => {
          this.$refs.form.validateField('scenario_id');
        });
      }
    },

    getParams() {
      const { process_methods, audit_methods, scenario_id, molds } = this.form;

      const baseParams = {};
      if (molds.length > 0) {
        baseParams.molds = molds;
      }

      if (process_methods.length === 1) {
        return this.getSingleProcessParams(
          process_methods[0],
          baseParams,
          scenario_id,
        );
      }

      if (process_methods.length === 2) {
        return this.getAuditParams(audit_methods, baseParams, scenario_id);
      }
    },

    getSingleProcessParams(method, baseParams, scenario_id) {
      const methodMap = {
        extract: { ...baseParams, task_type: 'extract' },
        intelligent: {
          ...baseParams,
          scenario_id,
          task_type: 'judge',
        },
      };

      return methodMap[method];
    },

    getAuditParams(audit_methods, baseParams, scenario_id) {
      const auditParams = { ...baseParams, task_type: 'audit' };

      if (audit_methods.length === 1 && audit_methods[0] === 'rule') {
        return auditParams;
      }

      if (audit_methods.includes('judge')) {
        return { ...auditParams, scenario_id };
      }
    },

    resetBaseForm() {
      this.form = {
        ...this.form,
        process_methods: [],
        audit_methods: [],
        scenario_id: [],
        molds: [],
      };
    },
  },
};
