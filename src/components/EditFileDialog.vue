<template>
  <el-dialog
    title="修改文件"
    :visible="true"
    width="600px"
    :close-on-click-modal="false"
    :modal-append-to-body="false"
    @close="handleClose">
    <div class="edit-dialog-content">
      <el-form
        :model="form"
        :rules="formRules"
        ref="form"
        label-width="100px"
        size="small">
        <el-form-item label="文件名称" prop="filename">
          <el-input
            v-model="form.filename"
            placeholder="请输入文件名称"
            style="width: 100%">
          </el-input>
        </el-form-item>

        <el-form-item label="处理方式" prop="process_methods">
          <el-checkbox-group
            v-model="form.process_methods"
            :disabled="isProcessMethodsDisabled"
            @change="handleProcessMethodChange">
            <el-checkbox label="extract">要素提取</el-checkbox>
            <el-checkbox label="intelligent">智能审核</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item prop="audit_methods">
          <span slot="label">
            <el-tooltip
              content="如要审核，至少选择一种审核方式"
              placement="left">
              <i
                class="el-icon-question"
                style="margin-right: 4px; color: #909399"></i>
            </el-tooltip>
            审核方式
          </span>
          <el-checkbox-group
            v-model="form.audit_methods"
            :disabled="isAuditMethodsDisabled"
            @change="handleAuditMethodChange">
            <el-checkbox
              label="rule"
              :disabled="isAuditMethodsDisabled || isRuleAuditDisabled"
              >规则审核</el-checkbox
            >
            <el-checkbox label="judge" :disabled="isAuditMethodsDisabled"
              >大模型审核</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>

        <el-form-item prop="molds" label="Schema">
          <el-select
            v-model="form.molds"
            multiple
            placeholder="请选择Schema"
            style="width: 100%"
            :disabled="isSchemaConfigDisabled">
            <el-option
              v-for="(label, index) in schemas.items"
              :key="index"
              :disabled="label.disabled"
              :label="getSelectOptionLabel(label)"
              :value="label.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item prop="scenario_id">
          <span slot="label">
            <el-tooltip placement="left">
              <div slot="content">
                选择大模型审核后，必须选择<br />
                应用场景，大模型审核规则可<br />
                使用与应用场景有关的审核点<br />
                来审核合同
              </div>
              <i
                class="el-icon-question"
                style="margin-right: 4px; color: #909399"></i>
            </el-tooltip>
            应用场景
          </span>
          <el-select
            v-model="form.scenario_id"
            placeholder="请选择应用场景"
            style="width: 100%"
            :disabled="isScenarioConfigDisabled">
            <el-option
              v-for="item in scenarioOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="submitting">
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import fileFormMixin from '@/mixins/fileFormMixin';

export default {
  name: 'EditFileDialog',
  mixins: [fileFormMixin],

  props: {
    fileData: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      form: {
        filename: '',
        process_methods: [],
        audit_methods: [],
        scenario_id: '',
        molds: [],
      },
      submitting: false,
    };
  },

  computed: {
    formRules() {
      return {
        filename: [
          {
            required: true,
            message: '请输入文件名称',
            trigger: 'blur',
          },
        ],
        ...this.baseFormRules,
      };
    },
  },

  created() {
    this.initFormData();
  },

  methods: {
    initFormData() {
      // 从传入的文件数据中回填表单
      this.form = {
        filename: this.fileData.filename || '',
        process_methods: this.fileData.process_methods || [],
        audit_methods: this.fileData.audit_methods || [],
        scenario_id: this.fileData.scenario_id || '',
        molds: this.fileData.molds || [],
      };

      // 数据回填后清除验证状态，确保验证规则正确应用
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    },

    handleConfirm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.submitting = true;

          // 获取表单参数（不包含文件相关）
          const params = this.getParams();
          const editData = {
            ...params,
            filename: this.form.filename,
          };

          this.$emit('confirm', editData);
        } else {
          this.$message.error('请完善表单信息');
          return false;
        }
      });
    },

    handleClose() {
      this.$emit('close');
      this.resetForm();
    },

    resetForm() {
      this.form = {
        filename: '',
        process_methods: [],
        audit_methods: [],
        scenario_id: [],
        molds: [],
      };
      this.submitting = false;

      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.resetFields();
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.edit-dialog-content {
  .el-form {
    margin-bottom: 20px;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
