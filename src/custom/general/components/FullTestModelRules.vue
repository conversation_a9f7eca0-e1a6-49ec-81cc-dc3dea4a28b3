<template>
  <div v-loading="isLoading">
    <div class="container">
      <div class="header" :style="headerStyle">
        <el-tooltip content="返回" placement="top">
          <el-button
            @click="handleGoBack"
            type="text"
            :size="buttonSize"
            circle>
            <i class="el-icon-back" style="font-size: 20px"></i>
          </el-button>
        </el-tooltip>
        <span>选择文档</span>
        <el-select
          :size="selectSize"
          v-model="chatdoc_unique"
          placeholder="请选择测试文档"
          :style="documentSelectStyle">
          <el-option
            v-for="item in documentList"
            :key="item.id"
            :value="item.chatdoc_unique"
            :label="item.name"></el-option>
        </el-select>
        <span>选择法规</span>
        <el-select
          multiple
          collapse-tags
          :size="selectSize"
          v-model="orderIds"
          placeholder="请选择法规"
          class="law-select"
          :style="lawSelectStyle"
          popper-class="law-select-popper"
          @change="handleLawChange">
          <el-option
            v-for="item in lawsOptions"
            :key="item.id"
            :value="item.id"
            :label="item.name">
            <tooltip-over :content="item.name" :width="300"></tooltip-over>
          </el-option>
        </el-select>
        <el-tooltip content="开始测试" placement="top">
          <el-button type="primary" :size="buttonSize" @click="handleStartTest">
            <theme-icon name="test-law-white"></theme-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="提交审核" placement="top">
          <el-button
            type="primary"
            :size="buttonSize"
            @click="handleSubmitReview">
            <theme-icon name="review-white"></theme-icon>
          </el-button>
        </el-tooltip>
      </div>

      <div class="search-form">
        <el-select
          v-model="modificationFilter"
          size="medium"
          class="modification-filter"
          @change="handleSearchClick">
          <el-option label="全部" value="all"></el-option>
          <el-option label="已修改" value="modified"></el-option>
          <el-option label="未修改" value="unmodified"></el-option>
        </el-select>
        <el-select
          v-model="localSearchForm.field"
          size="medium"
          class="search-field">
          <el-option
            v-for="(item, index) in searchOptions"
            :key="index"
            :label="item.label"
            :value="item.value"></el-option>
        </el-select>
        <el-input
          v-model.trim="localSearchForm.keyword"
          :placeholder="currentSearchOption.placeholder"
          size="medium"
          clearable
          class="search-input"
          @clear="handleSearchClick"
          @keydown.enter.native="handleSearchClick">
        </el-input>
        <el-button
          type="primary"
          size="medium"
          class="search-btn"
          @click="handleSearchClick">
          查询
        </el-button>
      </div>
    </div>
    <el-table
      :data="tableData"
      border
      :height="tableHeight"
      ref="table"
      class="rule-list-table has-border"
      :cell-class-name="getCellClassName"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55"> </el-table-column>
      <el-table-column
        width="100"
        prop="id"
        label="ID"
        header-align="center"></el-table-column>
      <el-table-column
        min-width="100"
        prop="order.name"
        label="法规名称"
        header-align="center"></el-table-column>
      <el-table-column
        min-width="100"
        prop="nowData.name"
        label="规则名称"
        align="center">
        <template slot-scope="scope">
          {{ scope.row.nowData.name }}
        </template>
      </el-table-column>
      <el-table-column
        min-width="300"
        prop="nowData.rule_content"
        label="法规内容"
        header-align="center"></el-table-column>
      <el-table-column
        min-width="300"
        label="核心要求"
        header-align="center"
        prop="nowData.core">
        <template slot-scope="scope">
          <div class="editable-cell-content">
            <editable-textarea
              v-model="scope.row.nowData.core"
              :disabled="isRowWaiting(scope.row)"
              placeholder="请输入核心要求"
              :default-div="true"
              class="inline-editable-textarea"
              @blur="handleRowEditCheck(scope.row)" />
          </div>
        </template>
      </el-table-column>
      <el-table-column
        min-width="400"
        header-align="center"
        label="验证方式"
        prop="nowData.check_method">
        <template slot-scope="scope">
          <div class="editable-cell-content">
            <editable-textarea
              v-model="scope.row.nowData.check_method"
              :disabled="isRowWaiting(scope.row)"
              placeholder="请输入验证方式"
              :default-div="true"
              class="inline-editable-textarea"
              @blur="handleRowEditCheck(scope.row)" />
          </div>
        </template>
      </el-table-column>
      <el-table-column width="100" align="center">
        <template slot="header" slot-scope="{}">
          <el-popover
            placement="bottom"
            width="160"
            popper-class="model-rule-column-select-popper"
            trigger="click">
            <div
              @click="handleStatusAllClick"
              class="option-item"
              :class="{
                'is-active': filterStatus.length === columnStatusNums,
              }">
              <span>全部</span>
              <i class="el-icon-check"></i>
            </div>
            <div
              v-for="(value, key) in ROW_STATUS_MAP"
              :key="value"
              class="option-item"
              :class="{
                'is-active': filterStatus.includes(key),
              }"
              @click="handleLawStatusItemClick(key)">
              <span>{{ value }}</span>
              <i class="el-icon-check"></i>
            </div>
            <span slot="reference" class="option-header">
              是否合规<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <el-tooltip
            v-if="
              hasTestResult(scope.row.id) &&
              scope.row.nowData.compliance !== ROW_STATUS.WAITING
            "
            placement="top"
            effect="dark"
            :open-delay="300">
            <div slot="content" class="tooltip-content">
              <div v-if="testResults[scope.row.id].judgment_basis">
                <strong>判断依据：</strong><br />
                {{ testResults[scope.row.id].judgment_basis }}
              </div>
              <div
                v-if="testResults[scope.row.id].suggestion"
                class="suggestion-section">
                <strong>建议：</strong><br />
                {{ testResults[scope.row.id].suggestion }}
              </div>
            </div>
            <div
              class="compliance-status with-tooltip"
              :class="getComplianceStatusClass(scope.row.nowData.compliance)">
              {{ ROW_STATUS_MAP[scope.row.nowData.compliance] }}
              <i class="el-icon-info info-icon"></i>
            </div>
          </el-tooltip>
          <div
            v-else
            class="compliance-status"
            :class="getComplianceStatusClass(scope.row.nowData.compliance)">
            {{ ROW_STATUS_MAP[scope.row.nowData.compliance] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        width="200"
        label="操作"
        header-align="center"
        fixed="right">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="handleDetail(scope.row)"
            :disabled="isRowWaiting(scope.row)"
            >详情</el-button
          >
          <el-button type="text" @click="handleRelate(scope.row)"
            >关联法规</el-button
          >
          <el-button
            type="text"
            @click="handleReTest(scope.row)"
            :disabled="isRowWaiting(scope.row)"
            >重新测试</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <related-laws-dialog
      v-if="showRelatedLawsDialog"
      :current-row="relatedRow"
      :laws-options="relateLawsOptions"
      @close="handleRelatedLawsDialogClose" />
  </div>
</template>
<script>
import RelatedLawsDialog from './RelatedLawsDialog.vue';
import EditableTextarea from './EditableTextarea.vue';
import TooltipOver from '../components/TooltipOver.vue';
import { laws as lawsApi } from '@/store/api';
import _ from 'lodash';
import { REVIEW_STATUS, CHECK_TYPE_MAP } from '@/store/constants';
import { mapGetters } from 'vuex';

const SCREEN_BREAKPOINTS = {
  MEDIUM: 1550,
};

const ROW_STATUS = {
  WAITING: 'WAITING',
  NOT_COMPLIANCE: 'NOT_COMPLIANCE',
  COMPLIANCE: 'COMPLIANCE',
  UNCERTAIN: 'UNCERTAIN',
  NOT_APPLICABLE: 'NOT_APPLICABLE',
};
export default {
  name: 'full-test-model-rules',
  components: {
    RelatedLawsDialog,
    EditableTextarea,
    TooltipOver,
  },
  props: {
    lawsOptions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      ROW_STATUS,
      chatdoc_unique: '',
      orderIds: [],
      tableData: [],
      formateTableOriginData: [],
      ROW_STATUS_MAP: {
        [ROW_STATUS.COMPLIANCE]: '合规',
        [ROW_STATUS.NOT_COMPLIANCE]: '不合规',
        [ROW_STATUS.WAITING]: '分析中',
        [ROW_STATUS.NOT_APPLICABLE]: '不适用',
        [ROW_STATUS.UNCERTAIN]: '-',
      },
      filterStatus: [],
      showRelatedLawsDialog: false,
      localSearchForm: {
        field: 'law_name',
        keyword: '',
      },
      modificationFilter: 'all',
      searchOptions: [
        { label: '法规名称', value: 'law_name', placeholder: '请输入法规名称' },
        {
          label: '法规内容',
          value: 'rule_content',
          placeholder: '请输入法规内容',
        },
        { label: '规则名称', value: 'name', placeholder: '请输入规则名称' },
      ],
      isLoading: false,
      documentList: [],
      selectedRows: [], // 存储选中的行
      testResults: {}, // 存储测试结果 {rowId: {judgment_basis, suggestion, compliance}}
      testQueue: [],
      runningTests: new Set(),
      maxConcurrency: 2,
      originalStatusMap: new Map(),
      abortControllers: new Map(),
      firstEditNotifiedRows: new Set(),
      relatedRow: null,
      windowWidth: window.innerWidth, // 响应式窗口宽度
    };
  },
  computed: {
    ...mapGetters(['loginUser']),
    columnStatusNums() {
      return Object.keys(this.ROW_STATUS_MAP).length;
    },
    currentSearchOption() {
      return (
        this.searchOptions.find(
          (option) => option.value === this.localSearchForm.field,
        ) || this.searchOptions[0]
      );
    },
    relateLawsOptions() {
      return this.lawsOptions.filter((item) => this.orderIds.includes(item.id));
    },
    hasSelectedRows() {
      return this.selectedRows.length > 0;
    },
    testableSelectedRows() {
      return this.selectedRows.filter(
        (row) => row.nowData.compliance !== this.ROW_STATUS.WAITING,
      );
    },
    hasSelectedDocument() {
      return !!this.chatdoc_unique;
    },
    tableHeight() {
      // 根据屏幕宽度动态计算表格高度
      const baseHeight = window.innerHeight;
      const isNarrowScreen = this.windowWidth <= SCREEN_BREAKPOINTS.MEDIUM;

      if (isNarrowScreen) {
        return `${baseHeight - 240}px`;
      } else {
        return `${baseHeight - 142}px`;
      }
    },
    buttonSize() {
      if (this.windowWidth <= SCREEN_BREAKPOINTS.MEDIUM) {
        return 'mini';
      } else {
        return 'medium';
      }
    },
    selectSize() {
      if (this.windowWidth <= SCREEN_BREAKPOINTS.MEDIUM) {
        return 'mini';
      } else {
        return 'small';
      }
    },
    documentSelectStyle() {
      if (this.windowWidth <= SCREEN_BREAKPOINTS.MEDIUM) {
        return { width: '283px' };
      } else {
        return { width: '300px' };
      }
    },
    lawSelectStyle() {
      if (this.windowWidth <= SCREEN_BREAKPOINTS.MEDIUM) {
        return { width: '283px' };
      } else {
        return { width: '300px' };
      }
    },
    headerStyle() {
      if (this.windowWidth <= SCREEN_BREAKPOINTS.MEDIUM) {
        return { 'column-gap': '10px' };
      } else {
        return { 'column-gap': '20px' };
      }
    },
  },
  created() {
    this.init();
  },

  mounted() {
    this.handleResize = () => {
      this.windowWidth = window.innerWidth;
    };
    window.addEventListener('resize', this.handleResize);
  },

  beforeDestroy() {
    this.resetStatus();
    if (this.handleResize) {
      window.removeEventListener('resize', this.handleResize);
    }
  },
  methods: {
    init() {
      this.getCheckPoints();
      this.getDocumentList();
    },
    resetStatus() {
      this.abortControllers.forEach((controller, rowId) => {
        try {
          controller.abort();
        } catch (error) {
          console.warn(`取消请求失败 (rowId: ${rowId}):`, error);
        }
      });

      this.testQueue = [];
      this.runningTests.clear();
      this.originalStatusMap.clear();
      this.abortControllers.clear();
      this.firstEditNotifiedRows.clear();
      this.relatedRow = null;
      this.formateTableOriginData = [];
      this.tableData = [];
    },
    async handleRowEditCheck(row) {
      const isCurrentlyEdited = this.hasEditedData(row);

      if (isCurrentlyEdited && !this.firstEditNotifiedRows.has(row.id)) {
        try {
          const data = await lawsApi.getCheckPointInfo(row.id);
          if (
            data.draft &&
            data.draft.review_status === REVIEW_STATUS.NOT_PASS
          ) {
            data.review_status = REVIEW_STATUS.NOT_PASS;
            delete data.draft;
          }
          const { review_status, updated_by_id } = data.draft
            ? data.draft
            : data;
          if (
            ![
              REVIEW_STATUS.NOT_REVIEWED,
              REVIEW_STATUS.DEL_NOT_REVIEWED,
            ].includes(review_status) ||
            !updated_by_id ||
            updated_by_id === this.loginUser.id
          ) {
            return;
          }
          this.$alert(
            '当前审核点正在审核中，请通知审核人员进行审核；审核通过后，本次修改的内容才能再次提交； 注：修改内容不影响本次测试过程；',
            '提示',
            {
              confirmButtonText: '确定',
              customClass: 'first-edit-alert',
            },
          );
          this.firstEditNotifiedRows.add(row.id);
        } catch (error) {
          this.$notify({
            title: '错误',
            message: error.message,
            type: 'error',
          });
        }
        if (this.modificationFilter === 'unmodified') {
          // 如果筛选是未修改，需要更新表格
          this.tableData = this.filterTableData();
        }
      }

      if (!isCurrentlyEdited && this.firstEditNotifiedRows.has(row.id)) {
        this.firstEditNotifiedRows.delete(row.id);
      }
    },
    handleRelatedLawsDialogClose() {
      this.relatedRow = null;
      this.showRelatedLawsDialog = false;
    },
    getCellClassName({ row, column, columnIndex }) {
      if (!column.property) {
        return '';
      }
      if (column.property.startsWith('nowData.')) {
        const fieldName = column.property.replace('nowData.', '');
        const originalValue = row[fieldName];
        const currentValue = row.nowData && row.nowData[fieldName];
        if (originalValue !== currentValue) {
          // 检查下一列是否也是changed状态
          const tableColumns = this.$refs.table ? this.$refs.table.columns : [];
          const nextColumn = tableColumns[columnIndex + 1];
          let hasNextChanged = false;

          if (
            nextColumn &&
            nextColumn.property &&
            nextColumn.property.startsWith('nowData.')
          ) {
            const nextFieldName = nextColumn.property.replace('nowData.', '');
            const nextOriginalValue = row[nextFieldName];
            const nextCurrentValue = row.nowData && row.nowData[nextFieldName];
            hasNextChanged = nextOriginalValue !== nextCurrentValue;
          }

          // 根据相邻状态返回不同的类名
          if (hasNextChanged) {
            return 'cell-changed cell-changed-with-next';
          } else {
            return 'cell-changed';
          }
        }
      }

      return '';
    },
    handleLawChange() {
      this.getCheckPoints();
    },
    async handleSelectionChange(rows) {
      this.selectedRows = rows;
    },

    hasEditedData(row) {
      const checkField = [
        'name',
        'rule_content',
        'scenario_ids',
        'subject',
        'check_type',
        'core',
        'check_method',
      ];
      const isEdit = checkField.some((fieldName) => {
        if (Array.isArray(row[fieldName])) {
          return !_.isEqual(
            _.sortBy(row[fieldName]),
            _.sortBy(row.nowData[fieldName]),
          );
        }
        return !_.isEqual(row[fieldName], row.nowData[fieldName]);
      });

      return isEdit;
    },

    // 将选中的行移动到顶部并排序
    sortSelectedRowsToTop() {
      if (this.selectedRows.length === 0) {
        return;
      }

      const selectedIds = this.selectedRows.map((row) => row.id);
      const selectedRowsData = this.tableData.filter((row) =>
        selectedIds.includes(row.id),
      );
      const unselectedRowsData = this.tableData.filter(
        (row) => !selectedIds.includes(row.id),
      );

      // 选中的行按ID排序
      selectedRowsData.sort((a, b) => a.id - b.id);

      // 重新组合数据：选中的在前，未选中的在后
      this.tableData = [...selectedRowsData, ...unselectedRowsData];
    },
    async getDocumentList() {
      try {
        const { files } = await lawsApi.getTestFileList();
        this.documentList = files;
      } catch (error) {
        this.$notify({
          title: '错误',
          message: error.message,
          type: 'error',
        });
      }
    },

    async getCheckPoints(options = { loading: true }) {
      try {
        if (this.orderIds.length === 0) {
          this.resetStatus();
          return;
        }
        if (options.loading) {
          this.isLoading = true;
        }
        const { items } = await lawsApi.getCheckPoints({
          order_ids: this.orderIds,
          abandoned: false,
          size: 1000,
          page: 1,
        });
        const formateTableAllData = this.formatTableData(items);
        const updatedData =
          this.updateTableDataWithTestResults(formateTableAllData);
        this.formateTableOriginData = _.cloneDeep(updatedData);
        this.tableData = this.filterTableData(updatedData);
      } catch (error) {
        this.$notify({
          title: '错误',
          message: error.message,
          type: 'error',
        });
      } finally {
        if (options.loading) {
          this.isLoading = false;
        }
      }
    },
    updateTableDataWithTestResults(tableData) {
      return tableData.map((row) => {
        if (this.testResults[row.id] && this.testResults[row.id].compliance) {
          return {
            ...row,
            nowData: {
              ...row.nowData,
              compliance: this.testResults[row.id].compliance,
            },
          };
        }
        return row;
      });
    },
    filterTableData() {
      let filteredData = [...this.formateTableOriginData];
      filteredData = this.applyModificationFilter(filteredData);
      filteredData = this.applyComplianceStatusFilter(filteredData);
      filteredData = this.applyKeywordFilter(filteredData);
      return filteredData;
    },
    applyModificationFilter(data) {
      if (this.modificationFilter === 'modified') {
        return data.filter((item) => this.hasEditedData(item));
      }
      if (this.modificationFilter === 'unmodified') {
        return data.filter((item) => !this.hasEditedData(item));
      }
      if (this.modificationFilter === 'all') {
        return data;
      }
      return data;
    },
    applyComplianceStatusFilter(data) {
      if (this.filterStatus.length === 0) {
        return data;
      }
      return data.filter((item) =>
        this.filterStatus.includes(item.nowData.compliance),
      );
    },
    applyKeywordFilter(data) {
      if (!this.localSearchForm.keyword) {
        return data;
      }

      const keyword = this.localSearchForm.keyword.toLowerCase();
      const field = this.localSearchForm.field;

      return data.filter((item) => {
        const fieldValue = item.nowData[field];
        return fieldValue && fieldValue.toLowerCase().includes(keyword);
      });
    },
    formatTableData(data) {
      return data.map((item) => {
        if (item.draft && item.draft.review_status === REVIEW_STATUS.NOT_PASS) {
          item.review_status = REVIEW_STATUS.NOT_PASS;
          item.meta = item.draft.meta;
          delete item.draft;
        }
        const nowData = item.draft ? item.draft : item;
        const {
          check_method,
          check_type,
          core,
          id,
          name,
          review_status,
          rule_content,
          subject,
          updated_by_id,
          reviewer_id,
          meta,
          scenarios,
        } = nowData;
        const order = item.order;
        return {
          ...item,
          scenario_ids: scenarios.map((item) => item.id),
          check_method,
          check_type,
          core,
          name,
          review_status,
          rule_content,
          subject,
          updated_by_id,
          reviewer_id,
          meta,
          scenarios,
          nowData: {
            check_method,
            check_type,
            core,
            id,
            name,
            review_status,
            rule_content,
            subject,
            updated_by_id,
            reviewer_id,
            meta,
            // 如果draft 无scenarios数据，则使用外部scenarios
            scenarios: scenarios.length > 0 ? scenarios : item.scenarios,
            scenario_ids:
              scenarios.length > 0
                ? scenarios.map((item) => item.id)
                : item.scenarios.map((item) => item.id),
            law_name: order.name,
            compliance: ROW_STATUS.UNCERTAIN,
          },
        };
      });
    },
    async handleSearchClick() {
      this.tableData = this.filterTableData();
      // 筛选后重新应用选中状态和排序
      await this.$nextTick();
      this.sortSelectedRowsToTop();
    },

    handleStatusAllClick() {
      if (this.filterStatus.length < this.columnStatusNums) {
        this.filterStatus = Object.keys(this.ROW_STATUS_MAP);
      } else {
        this.filterStatus = [];
      }
      this.handleSearchClick();
    },
    handleLawStatusItemClick(val) {
      const isExist = this.filterStatus.includes(val);
      if (isExist) {
        this.filterStatus = this.filterStatus.filter((item) => {
          return item !== val;
        });
      } else {
        this.filterStatus.push(val);
      }
      this.handleSearchClick();
    },
    handleGoBack() {
      this.$emit('goBack');
    },
    handleDetail(row) {
      this.$emit('detail', row);
    },
    handleRelate(row) {
      this.relatedRow = row;
      this.showRelatedLawsDialog = true;
    },
    handleReTest(row) {
      if (!this.hasSelectedDocument) {
        this.showErrorNotification('请先选择文档');
        return;
      }
      if (this.isRowWaiting(row)) {
        this.showWarningNotification('该行正在分析中，请稍候');
        return;
      }
      this.startSingleRowTest(row);
    },
    startSingleRowTest(row) {
      this.originalStatusMap.set(row.id, row.nowData.compliance);
      this.updateRowFieldValue(row.id, 'compliance', this.ROW_STATUS.WAITING);
      this.addToTestQueue([row]);
      this.startConcurrentTesting();
    },

    updateRowData(id, data) {
      this.updateRowSomeData(id, data);
    },
    updateRowSomeData(rowId, data) {
      const originRowIndex = this.formateTableOriginData.findIndex(
        (row) => row.id === rowId,
      );
      const newData = {
        ...this.formateTableOriginData[originRowIndex].nowData,
        ...data,
      };

      if (originRowIndex !== -1) {
        this.$set(
          this.formateTableOriginData[originRowIndex],
          'nowData',
          newData,
        );
      }

      const rowIndex = this.tableData.findIndex((row) => row.id === rowId);

      if (rowIndex !== -1) {
        this.$set(this.tableData[rowIndex], 'nowData', newData);
      }
      this.handleRowEditCheck(this.tableData[rowIndex]);
    },

    /**
     * 开始批量测试
     */
    async handleStartTest() {
      const validation = this.validateTestConditions();
      if (!validation.valid) {
        this.showErrorNotification(validation.message);
        return;
      }

      this.startBatchTest(this.testableSelectedRows);
    },

    /**
     * 开始批量测试
     * @param {Array} rows - 要测试的行数据数组
     */
    startBatchTest(rows) {
      if (rows.length === 0) return;
      rows.forEach((row) => {
        this.originalStatusMap.set(row.id, row.nowData.compliance);
        this.updateRowFieldValue(row.id, 'compliance', this.ROW_STATUS.WAITING);
      });

      this.addToTestQueue(rows);
      this.startConcurrentTesting();
    },
    addToTestQueue(rows) {
      const existingIds = new Set([
        ...this.testQueue.map((row) => row.id),
        ...this.runningTests,
      ]);

      const newRows = rows.filter((row) => !existingIds.has(row.id));
      this.testQueue.push(...newRows);
    },
    startConcurrentTesting() {
      while (
        this.testQueue.length > 0 &&
        this.runningTests.size < this.maxConcurrency
      ) {
        const row = this.testQueue.shift();
        if (row && !this.runningTests.has(row.id)) {
          this.runningTests.add(row.id);
          this.testSingleRowWithQueue(row);
        }
      }
    },

    async testSingleRowWithQueue(row) {
      try {
        await this.testSingleRow(row);
      } catch (error) {
        this.$notify({
          title: '错误',
          message: error.message,
          type: 'error',
        });
      } finally {
        this.runningTests.delete(row.id);
        this.startConcurrentTesting();
      }
    },

    async testSingleRow(row) {
      const originalStatus = this.originalStatusMap.get(row.id);
      const abortController = new AbortController();
      this.abortControllers.set(row.id, abortController);

      try {
        this.updateRowFieldValue(row.id, 'compliance', this.ROW_STATUS.WAITING);
        const { rule_content, name, subject, check_type, core, check_method } =
          row.nowData;
        const requestData = {
          rule_content,
          name,
          subject,
          check_type,
          core,
          check_method,
          chatdoc_unique: this.chatdoc_unique,
        };
        const response = await lawsApi.ContractAnalysisByCheckPoint(
          row.nowData.id,
          requestData,
          { signal: abortController.signal },
        );

        if (response) {
          let complianceStatus;
          if (response.compliance_status) {
            complianceStatus = Object.keys(this.ROW_STATUS_MAP).find(
              (key) => this.ROW_STATUS_MAP[key] === response.compliance_status,
            );
            if (response.compliance_status) {
              complianceStatus = complianceStatus || ROW_STATUS.NOT_APPLICABLE;
            }
            this.updateRowFieldValue(row.id, 'compliance', complianceStatus);
          } else {
            complianceStatus = ROW_STATUS.UNCERTAIN;
            this.updateRowFieldValue(
              row.id,
              'compliance',
              ROW_STATUS.UNCERTAIN,
            );
          }

          this.$set(this.testResults, row.id, {
            judgment_basis: response.judgment_basis || '',
            suggestion: response.suggestion || '',
            compliance: complianceStatus,
          });
        } else {
          this.updateRowFieldValue(row.id, 'compliance', ROW_STATUS.UNCERTAIN);
          this.$set(this.testResults, row.id, {
            judgment_basis: '',
            suggestion: '',
            compliance: ROW_STATUS.UNCERTAIN,
          });
        }
        this.originalStatusMap.delete(row.id);
        this.abortControllers.delete(row.id);
      } catch (error) {
        this.updateRowFieldValue(row.id, 'compliance', originalStatus);
        this.originalStatusMap.delete(row.id);
        this.abortControllers.delete(row.id);

        this.$notify({
          title: '测试失败',
          message: `ID ${row.id} 测试失败: ${error.message}`,
          type: 'error',
        });
        console.error(`测试行 ${row.id} 失败:`, error);
      }
    },

    // 更新行的合规状态
    updateRowFieldValue(rowId, field, newStatus) {
      const rowIndex = this.tableData.findIndex((row) => row.id === rowId);
      if (rowIndex !== -1) {
        this.$set(this.tableData[rowIndex].nowData, field, newStatus);
      }

      // 同时更新原始数据
      const originRowIndex = this.formateTableOriginData.findIndex(
        (row) => row.id === rowId,
      );
      if (originRowIndex !== -1) {
        this.$set(
          this.formateTableOriginData[originRowIndex].nowData,
          field,
          newStatus,
        );
      }
    },
    hasTestResult(rowId) {
      return rowId in this.testResults;
    },
    getComplianceStatusClass(compliance) {
      return {
        'compliance-success': compliance === this.ROW_STATUS.COMPLIANCE,
        'compliance-error': compliance === this.ROW_STATUS.NOT_COMPLIANCE,
      };
    },
    isRowWaiting(row) {
      return row.nowData.compliance === this.ROW_STATUS.WAITING;
    },
    validateTestConditions() {
      if (!this.hasSelectedDocument) {
        return { valid: false, message: '请先选择文档' };
      }
      if (this.orderIds.length === 0) {
        return { valid: false, message: '请选择法规' };
      }
      if (!this.hasSelectedRows) {
        return { valid: false, message: '请至少勾选一行数据' };
      }
      if (this.testableSelectedRows.length === 0) {
        return { valid: false, message: '所选行均在分析中，请稍候' };
      }
      return { valid: true, message: '' };
    },
    showErrorNotification(message) {
      this.$notify({
        title: '错误',
        message,
        type: 'error',
      });
    },
    showWarningNotification(message) {
      this.$notify({
        title: '提示',
        message,
        type: 'warning',
      });
    },
    showSuccessNotification(message) {
      this.$notify({
        title: '成功',
        message,
        type: 'success',
      });
    },

    // 提交审核
    async handleSubmitReview() {
      const editedRows = this.formateTableOriginData.filter((row) =>
        this.hasEditedData(row),
      );

      if (editedRows.length === 0) {
        this.$notify({
          title: '提示',
          message: '未检测到任何内容的调整，无须提交审核',
          type: 'warning',
        });
        return;
      }
      this.submitEditedData(editedRows);
    },
    getCheckTypeName(check_type) {
      const type = CHECK_TYPE_MAP.find((item) => item.value === check_type);
      return type ? type.label : '';
    },

    // 提交已编辑的数据
    async submitEditedData(editedRows) {
      try {
        this.isLoading = true;
        // 构建提交数据
        const submitData = editedRows.map((row) => {
          const data = {
            id: row.id,
            core: row.nowData.core,
            check_method: row.nowData.check_method,
            rule_content: row.nowData.rule_content,
            name: row.nowData.name,
            subject: row.nowData.subject,
            check_type: row.nowData.check_type,
          };
          if (
            !_.isEqual(
              _.sortBy(row.nowData.scenario_ids),
              _.sortBy(row.scenario_ids),
            )
          ) {
            data.scenario_ids = row.nowData.scenario_ids;
          }
          return data;
        });

        const { conflict_ids } = await lawsApi.updateCheckPoints({
          check_points: submitData,
        });
        if (conflict_ids.length > 0) {
          this.$alert(
            `检测到共有${conflict_ids.length}个审核点当前正在审核中，无法再次提交，请确认；点击复制，可粘贴到Excel中查看不能提交审核的审核点。`,
            '提示',
            {
              confirmButtonText: '复制',
              customClass: 'first-edit-alert',
              callback: () => {
                const header = [
                  'ID',
                  '法规名称',
                  '规则名称',
                  '法规内容',
                  '应用场景',
                  '核心要求',
                  '验证方式',
                  '行为主体',
                  '审核点类型',
                ];
                // 获取冲突行数据
                const rows = conflict_ids.map((id) => {
                  const row = this.formateTableOriginData.find(
                    (r) => r.id === id,
                  );
                  if (!row) return [];
                  return [
                    row.id,
                    row.nowData.law_name || '',
                    row.nowData.name || '',
                    row.nowData.rule_content || '',
                    row.order.scenarios
                      .filter((item) =>
                        row.nowData.scenario_ids.includes(item.id),
                      )
                      .map((item) => item.name)
                      .join('、'),
                    row.nowData.core || '',
                    row.nowData.check_method || '',
                    row.nowData.subject || '',
                    this.getCheckTypeName(row.nowData.check_type),
                  ];
                });
                // 组装为TSV格式（Excel可粘贴）
                const tsv = [header, ...rows]
                  .map((arr) => arr.join('\t'))
                  .join('\n');
                // 复制到剪贴板
                if (navigator.clipboard) {
                  navigator.clipboard
                    .writeText(tsv)
                    .then(() => {
                      this.$notify({
                        title: '复制成功',
                        message: '已复制冲突审核点数据，可粘贴到Excel查看',
                        type: 'success',
                      });
                    })
                    .catch(() => {
                      this.$notify({
                        title: '错误',
                        message: '复制失败',
                        type: 'error',
                      });
                    });
                } else {
                  // 兼容旧浏览器
                  const textarea = document.createElement('textarea');
                  textarea.value = tsv;
                  document.body.appendChild(textarea);
                  textarea.select();
                  try {
                    document.execCommand('copy');
                    this.$notify({
                      title: '复制成功',
                      message: '已复制冲突审核点数据，可粘贴到Excel查看',
                      type: 'success',
                    });
                  } catch (err) {
                    this.$notify({
                      title: '错误',
                      message: '复制失败',
                      type: 'error',
                    });
                  }
                  document.body.removeChild(textarea);
                }
              },
            },
          );
        } else {
          this.$notify({
            title: '成功',
            message: '提交审核成功',
            type: 'success',
          });
          this.getCheckPoints();
          this.$refs.table.clearSelection();
          this.selectedRows = [];
        }
      } catch (error) {
        this.$notify({
          title: '错误',
          message: error.message,
          type: 'error',
        });
      } finally {
        this.isLoading = false;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
// 断点常量（SCSS）与 CSS 变量，保持与 JS SCREEN_BREAKPOINTS 一致
$bp-mini: 1024px;
$bp-small: 1366px;
$bp-medium: 1500px;
:root {
  --bp-mini: 1024px;
  --bp-small: 1366px;
  --bp-medium: 1500px;
}

.container {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;

  .search-form {
    display: flex;
    align-items: center;
    gap: 10px;

    .modification-filter {
      width: 80px;
    }
    .search-field {
      width: 110px;
    }
    .search-input {
      width: 200px;
    }
  }
}
.header {
  display: flex;
  margin-bottom: 20px;
  align-items: center;
  min-height: 36px;

  .el-button {
    display: flex;
    align-items: center;
    justify-content: center;

    .el-icon-back {
      font-size: 16px;
    }

    .theme-icon {
      width: 16px;
      height: 16px;
    }

    &.el-button--medium {
      width: 36px;
      height: 36px;
    }

    &.el-button--small {
      width: 32px;
      height: 32px;

      .el-icon-back {
        font-size: 14px;
      }

      .theme-icon {
        width: 14px;
        height: 14px;
      }
    }

    &.el-button--mini {
      width: 28px;
      height: 28px;

      .el-icon-back {
        font-size: 12px;
      }

      .theme-icon {
        width: 12px;
        height: 12px;
      }
    }
    & + .el-button {
      margin: 0;
    }
  }

  @media (max-width: var(--bp-medium)) {
    column-gap: 12px;
    margin: 0 12px 12px;
  }
}
.law-select {
  ::v-deep .el-tag {
    max-width: 210px;
  }
}

// 表格行选中时的样式
::v-deep .el-table__row.current-row {
  background-color: #f0f9ff;
}

.compliance-status {
  display: inline-flex;
  align-items: center;

  &.with-tooltip {
    cursor: pointer;

    .info-icon {
      margin-left: 4px;
      font-size: 12px;
    }
  }

  .compliance-icon {
    margin-right: 4px;
    font-size: 14px;
  }

  &.compliance-success {
    color: #67c23a;
    font-weight: 500;
  }

  &.compliance-error {
    color: #f56c6c;
    font-weight: 500;
  }
}

.tooltip-content {
  max-width: 300px;
  line-height: 1.5;

  .suggestion-section {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
  }

  strong {
    color: #fff;
  }
}

// 可编辑单元格样式
.editable-cell-content {
  position: relative;
  min-height: 40px;
  border-radius: 4px;
  transition: all 0.3s ease;
  word-break: break-word;
  ::v-deep .modified-field {
    .el-input__inner,
    .el-textarea__inner {
      border-color: transparent !important;
    }
  }
}

// EditableTextarea内联样式
.inline-editable-textarea {
  width: 100%;

  ::v-deep .editable-textarea-wrapper {
    .el-textarea {
      .el-textarea__inner {
        border: 1px solid transparent;
        background-color: transparent;
        padding: 8px 12px;
        font-size: 14px;
        line-height: 1.5;
        resize: none;
        transition: all 0.3s ease;

        &:hover {
          border-color: #dcdfe6;
          background-color: #f5f7fa;
        }

        &:focus {
          border-color: #409eff;
          background-color: #fff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }
      }
    }
  }
}

// 数据变更单元格样式
::v-deep .cell-changed {
  background-color: rgba(64, 158, 255, 0.05);
  border-top: 2px solid #409eff !important;
  border-bottom: 2px solid #409eff !important;
  border-left: 2px solid #409eff !important;
  border-right: 2px solid #409eff !important;
  position: relative;
}

// 当前单元格有变更且下一个单元格也有变更时，移除右边框避免重叠
::v-deep .cell-changed-with-next {
  border-right: none !important;
}

// 确保最后一个单元格始终有右边框
::v-deep .cell-changed:last-child {
  border-right: 2px solid #409eff !important;
}
</style>
<style lang="scss">
.el-popover.model-rule-column-select-popper {
  padding: 10px 0;
  max-height: 388px;
  overflow-y: auto;
  .option-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 36px;
    padding: 0 20px;
    cursor: pointer;

    &:hover {
      background: #f3f7fc;
      color: $--color-primary;
    }

    .el-icon-check {
      visibility: hidden;
      color: $--color-primary;
    }

    &.is-active {
      background: #f3f7fc;
      color: $--color-primary;
      .el-icon-check {
        visibility: visible;
      }
    }
  }
}
.first-edit-alert {
  .el-message-box__btns {
    text-align: center;
  }
}
.law-select-popper {
  .el-select-dropdown__item {
    .over-flow {
      line-height: 34px;
    }
  }
}
</style>
