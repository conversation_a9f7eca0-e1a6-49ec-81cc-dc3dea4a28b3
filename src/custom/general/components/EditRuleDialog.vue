<template>
  <div class="dialog-container">
    <el-dialog
      v-loading="loading"
      :title="getDialogTitle()"
      :visible="true"
      :width="textExtractorDialogVisible ? '1400px' : '700px'"
      class="edit-review-rule-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :modal-append-to-body="false"
      @close="closeDialog">
      <div
        class="dialog-content"
        :class="{ 'with-extractor': textExtractorDialogVisible }">
        <div class="form-section">
          <el-form
            ref="form"
            :model="form"
            label-width="100px"
            label-position="left"
            :rules="rules">
            <el-form-item label="规则名称" prop="name">
              <div class="form-field-container">
                <el-input
                  v-model="form.name"
                  :readonly="isReadOnlyMode"
                  :class="{ 'modified-field': isFieldModified('name') }">
                </el-input>
                <span v-if="isFieldModified('name')" class="original-value">
                  <el-tooltip
                    :content="originalData.name"
                    placement="top"
                    popper-class="original-value-tip">
                    <span class="original-text">修改前</span>
                  </el-tooltip>
                </span>
              </div>
            </el-form-item>
            <el-form-item label="法规内容" prop="rule_content">
              <div class="form-field-container">
                <div class="form-container">
                  <el-input
                    type="textarea"
                    v-model.trim="form.rule_content"
                    readonly
                    :autosize="{ minRows: 2, maxRows: 6 }"
                    placeholder="请输入详细的法规内容"
                    :class="{
                      'modified-field': isFieldModified('rule_content'),
                    }"
                    style="width: 100%"></el-input>
                  <el-button type="text" @click="handleShowTextExtractorDialog">
                    原法规
                  </el-button>
                </div>
                <span
                  class="original-value"
                  v-if="isFieldModified('rule_content')">
                  <el-tooltip
                    :content="originalData.rule_content"
                    placement="top"
                    popper-class="original-value-tip">
                    <span class="original-text">修改前</span>
                  </el-tooltip>
                </span>
              </div>
            </el-form-item>
            <el-form-item label="应用场景" prop="scenario_ids">
              <div class="form-field-container">
                <el-input
                  readonly
                  :class="{
                    'modified-field': isFieldModified('scenario_ids'),
                  }"
                  :value="
                    nowData.scenarios
                      .filter((item) => form.scenario_ids.includes(item.id))
                      .map((item) => item.name)
                      .join('、')
                  "
                  v-if="isReadOnlyMode"></el-input>

                <el-select
                  v-else
                  multiple
                  v-model="form.scenario_ids"
                  placeholder="请选择应用场景"
                  style="width: 100%">
                  <el-option
                    v-for="item in nowData.scenarios"
                    :key="item.id"
                    :value="item.id"
                    :label="item.name"></el-option>
                </el-select>
                <span
                  v-if="isFieldModified('scenario_ids')"
                  class="original-value">
                  <el-tooltip
                    :content="getOriginalScenarioNames()"
                    placement="top"
                    popper-class="original-value-tip">
                    <span class="original-text">修改前</span>
                  </el-tooltip>
                </span>
              </div>
            </el-form-item>
            <el-form-item label="行为主体" prop="subject">
              <div class="form-field-container">
                <el-input
                  v-model="form.subject"
                  :readonly="isReadOnlyMode"
                  :class="{ 'modified-field': isFieldModified('subject') }">
                </el-input>
                <span v-if="isFieldModified('subject')" class="original-value">
                  <el-tooltip
                    :content="originalData.subject"
                    placement="top"
                    popper-class="original-value-tip">
                    <span class="original-text">修改前</span>
                  </el-tooltip>
                </span>
              </div>
            </el-form-item>
            <el-form-item label="审核点类型" prop="check_type">
              <div class="form-field-container">
                <div class="form-container">
                  <el-select
                    v-if="
                      [
                        EDIT_RULE_DIALOG_MODE.EDIT,
                        EDIT_RULE_DIALOG_MODE.SAVE,
                      ].includes(mode)
                    "
                    v-model="form.check_type"
                    placeholder=""
                    style="width: 100%">
                    <el-option
                      v-for="item in CHECK_TYPE_MAP"
                      :key="item.value"
                      :value="item.value"
                      :label="item.label"></el-option>
                  </el-select>
                  <el-input
                    v-else
                    :value="getCheckTypeName(form.check_type)"
                    :readonly="isReadOnlyMode"
                    :class="{
                      'modified-field': isFieldModified('check_type'),
                    }">
                  </el-input>
                  <el-tooltip placement="right">
                    <div slot="content">
                      <ul style="list-style: disc; padding-left: 20px">
                        <li>
                          禁止性条款：包含“不得”“禁止”等否定词，要求主体不做某事；
                        </li>
                        <li>
                          义务性条款：包含“应当”“必须”等肯定词，要求主体做某事；
                        </li>
                        <li>
                          程序性条款：规定行为流程、时限或形式（如“需在.前完成”“应通过.…方式”)
                        </li>
                      </ul>
                    </div>
                    <el-button type="text">说明</el-button>
                  </el-tooltip>
                  <span
                    v-if="isFieldModified('check_type')"
                    class="original-value">
                    <el-tooltip
                      :content="getCheckTypeName(originalData.check_type)"
                      placement="top"
                      popper-class="original-value-tip">
                      <span class="original-text">修改前</span>
                    </el-tooltip>
                  </span>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="核心要求" prop="core">
              <div class="form-field-container">
                <editable-textarea
                  v-model="form.core"
                  :readonly="isReadOnlyMode"
                  :is-modified="isFieldModified('core')"
                  placeholder="基金合同需约定投资者份额锁定期≥3个月，或设置与持有期限挂钩的短期赎回费用（赎回费用归入基金财产）。">
                </editable-textarea>
                <span v-if="isFieldModified('core')" class="original-value">
                  <el-tooltip
                    :content="originalData.core"
                    placement="top"
                    popper-class="original-value-tip">
                    <span class="original-text">修改前</span>
                  </el-tooltip>
                </span>
              </div>
            </el-form-item>
            <el-form-item label="验证方式" prop="check_method">
              <div class="form-field-container">
                <editable-textarea
                  v-model="form.check_method"
                  :readonly="isReadOnlyMode"
                  :is-modified="isFieldModified('check_method')"
                  placeholder="检查基金合同中是否包含“锁定期≥3个月”或“持有不满X个月赎回需支付Y%费用(费用归基金)”的条款（与风险关联：缺乏锁定期可能诱发投资者短期投机行为)">
                </editable-textarea>
                <span
                  v-if="isFieldModified('check_method')"
                  class="original-value">
                  <el-tooltip
                    :content="originalData.check_method"
                    placement="top"
                    popper-class="original-value-tip">
                    <span class="original-text">修改前</span>
                  </el-tooltip>
                </span>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div v-if="textExtractorDialogVisible" class="extractor-section">
          <text-extractor
            class="text-extractor"
            :readonly="isReadOnlyMode"
            :content="ruleContent"
            :loading="rullContentLoading"
            :old-content="form.rule_content"
            @close="textExtractorDialogVisible = false"
            @confirm="handleChangeRuleContent"></text-extractor>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog" size="small">取消</el-button>
        <template v-if="mode === EDIT_RULE_DIALOG_MODE.REVIEW">
          <el-button type="primary" @click="handleTest" size="small">
            开始测试
          </el-button>
          <el-button type="primary" @click="handleApprove" size="small">
            审核通过
          </el-button>
          <el-button type="primary" @click="handleReject" size="small">
            审核不通过
          </el-button>
        </template>
        <template v-else-if="mode === EDIT_RULE_DIALOG_MODE.EDIT">
          <el-button
            type="primary"
            @click="handleTest"
            size="small"
            :disabled="textExtractorDialogVisible">
            开始测试
          </el-button>
          <el-button
            type="primary"
            @click="handleSubmit"
            size="small"
            :disabled="textExtractorDialogVisible || notEdit">
            提交审核
          </el-button>
        </template>
        <template v-else-if="mode === EDIT_RULE_DIALOG_MODE.DELETE_REVIEW">
          <el-button type="primary" @click="handleTest" size="small">
            开始测试
          </el-button>
          <el-button type="danger" @click="handleDelete" size="small">
            确认删除
          </el-button>
          <el-button type="primary" @click="handleCancleDel" size="small">
            取消删除
          </el-button>
        </template>
        <template v-else-if="mode === EDIT_RULE_DIALOG_MODE.VIEW">
          <el-button type="primary" @click="handleTest" size="small">
            开始测试
          </el-button>
        </template>
        <template v-else-if="mode === EDIT_RULE_DIALOG_MODE.SAVE">
          <el-button type="primary" @click="handleCacheSave" size="small">
            保存
          </el-button>
        </template>
      </div>
    </el-dialog>

    <!-- 审核不通过原因弹窗 -->
    <el-dialog
      :visible.sync="rejectDialogVisible"
      width="500px"
      :close-on-click-modal="false">
      <el-input
        type="textarea"
        v-model="rejectReason"
        :autosize="{ minRows: 4, maxRows: 4 }"
        placeholder="请输入审核不通过的原因"
        style="width: 100%">
      </el-input>
      <div slot="title" class="reject-reason-title">
        <div>不通过原因<span class="required">（必填）</span></div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancelReject" size="small">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirmReject"
          :disabled="!rejectReason"
          size="small">
          提交
        </el-button>
      </div>
    </el-dialog>
    <test-model-rule-dialog
      v-if="testRuleDialogVisible"
      :document-list="documentList"
      :test-rule-info="form"
      :id="nowData.id"
      @close="handleTestDialogClose" />
  </div>
</template>
<script>
import TextExtractor from './TextExtractor.vue';
import TestModelRuleDialog from './TestModelRuleDialog.vue';
import EditableTextarea from './EditableTextarea.vue';
import { CHECK_TYPE_MAP, EDIT_RULE_DIALOG_MODE } from '@/store/constants.js';
import { laws as lawsApi } from '@/store/api';
import _ from 'lodash';

export default {
  name: 'edit-rule-dialog',
  props: {
    mode: {
      type: String,
      default: EDIT_RULE_DIALOG_MODE.EDIT,
    },
    originalData: {
      type: Object,
      default: () => ({}),
    },
    nowData: {
      type: Object,
      required: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    TextExtractor,
    TestModelRuleDialog,
    EditableTextarea,
  },
  computed: {
    isReviewMode() {
      return (
        this.mode === EDIT_RULE_DIALOG_MODE.REVIEW ||
        this.mode === EDIT_RULE_DIALOG_MODE.DELETE_REVIEW
      );
    },
    isReadOnlyMode() {
      return (
        this.mode === EDIT_RULE_DIALOG_MODE.REVIEW ||
        this.mode === EDIT_RULE_DIALOG_MODE.DELETE_REVIEW ||
        this.mode === EDIT_RULE_DIALOG_MODE.VIEW
      );
    },
    notEdit() {
      const keys = Object.keys(this.form);

      for (const k of keys) {
        const a = this.form[k];
        const b = this.nowData[k];
        if (Array.isArray(a) && Array.isArray(b)) {
          if (!_.isEqual(_.sortBy(a), _.sortBy(b))) {
            return false;
          }
        } else if (a !== b) {
          return false;
        }
      }
      return true;
    },
  },
  data() {
    return {
      CHECK_TYPE_MAP,
      EDIT_RULE_DIALOG_MODE,
      form: {
        name: '',
        scenario_ids: [],
        rule_content: '',
        subject: '',
        check_type: '',
        core: '',
        check_method: '',
      },
      rules: {
        name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
        scenario_ids: [
          {
            required: true,
            message: '请选择应用场景',
            trigger: 'change',
          },
        ],
        rule_content: [
          {
            required: true,
            message: '请输入法规内容',
          },
        ],
        subject: [
          { required: true, message: '请输入行为主体', trigger: 'blur' },
        ],
        check_type: [
          { required: true, message: '请选择审核点类型', trigger: 'change' },
        ],
        core: [{ required: true, message: '请输入核心要求', trigger: 'blur' }],
        check_method: [
          { required: true, message: '请输入验证方式', trigger: 'blur' },
        ],
      },
      textExtractorDialogVisible: false,
      rejectDialogVisible: false,
      rejectReason: '',
      testRuleDialogVisible: false,
      rullContentLoading: false,
      ruleContent: '',
      documentList: [],
    };
  },
  methods: {
    getDialogTitle() {
      switch (this.mode) {
        case EDIT_RULE_DIALOG_MODE.REVIEW:
        case EDIT_RULE_DIALOG_MODE.DELETE_REVIEW:
          return '审核规则复核';
        case EDIT_RULE_DIALOG_MODE.VIEW:
          return '查看规则';
        case EDIT_RULE_DIALOG_MODE.EDIT:
        default:
          return '修改审核规则';
      }
    },
    init() {
      const {
        name,
        rule_content,
        scenario_ids,
        subject,
        check_type,
        core,
        check_method,
      } = this.nowData;
      this.form = {
        name,
        rule_content,
        scenario_ids,
        subject,
        check_type,
        core,
        check_method,
      };
    },
    getCheckTypeName(check_type) {
      const type = CHECK_TYPE_MAP.find((item) => item.value === check_type);
      return type ? type.label : '';
    },
    handleChangeRuleContent(contents) {
      this.form.rule_content = contents.join('');
      this.textExtractorDialogVisible = false;
    },
    async handleShowTextExtractorDialog() {
      this.textExtractorDialogVisible = true;
      if (this.ruleContent) {
        return;
      }
      this.rullContentLoading = true;
      try {
        const { content } = await lawsApi.getRuleContent(this.nowData.ruleId);
        this.ruleContent = content;
      } catch (error) {
        this.$notify({
          title: '错误',
          message: error.message,
          type: 'error',
        });
      } finally {
        this.rullContentLoading = false;
      }
    },
    closeDialog() {
      this.$emit('close');
    },

    async handleTest() {
      if (!this.documentList.length) {
        this.getDocumentList();
      }
      const valid = await this.$refs.form.validate().catch(() => {});
      if (!valid) {
        return;
      }
      this.testRuleDialogVisible = true;
    },
    async getDocumentList() {
      try {
        const { files } = await lawsApi.getTestFileList();
        this.documentList = files;
      } catch (error) {
        this.$notify({
          title: '错误',
          message: error.message,
          type: 'error',
        });
      }
    },
    handleTestDialogClose() {
      this.testRuleDialogVisible = false;
    },
    async handleSubmit() {
      const valid = await this.$refs.form.validate().catch(() => {});
      if (!valid) {
        return;
      }
      this.$emit('submit', this.form);
    },
    async handleCacheSave() {
      const valid = await this.$refs.form.validate().catch(() => {});
      if (!valid) {
        return;
      }
      this.$emit('cache', this.form);
    },
    handleApprove() {
      this.$emit('approve');
    },
    handleReject() {
      this.rejectDialogVisible = true;
    },
    handleDelete() {
      this.$emit('delete');
    },
    handleCancleDel() {
      this.$emit('cancleDel');
    },
    handleConfirmReject() {
      this.$emit('reject', this.rejectReason);
      this.rejectDialogVisible = false;
      this.rejectReason = '';
    },
    handleCancelReject() {
      this.rejectDialogVisible = false;
      this.rejectReason = '';
    },
    isFieldModified(fieldName) {
      if (this.mode !== EDIT_RULE_DIALOG_MODE.REVIEW || !this.originalData) {
        return false;
      }
      return (
        JSON.stringify(this.form[fieldName]) !==
        JSON.stringify(this.originalData[fieldName])
      );
    },
    getOriginalScenarioNames() {
      if (!this.originalData.scenario_ids) {
        return '';
      }
      const names = this.originalData.scenario_ids
        .map((id) => {
          const option = this.nowData.scenarios.find((opt) => opt.id === id);
          return option ? option.name : '';
        })
        .filter((name) => name);
      return names.join('、');
    },
  },
  created() {
    this.init();
  },
};
</script>
<style lang="scss" scoped>
.dialog-container {
  .edit-review-rule-dialog {
    ::v-deep .el-dialog {
      margin: 0 !important;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      max-height: 90vh;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    ::v-deep .el-dialog__body {
      flex: 1;
      overflow: hidden;
      padding: 20px;
    }
  }
}

.dialog-content {
  display: flex;
  gap: 20px;
  overflow: hidden;

  &.with-extractor {
    .form-section {
      min-width: 0;
      padding-right: 10px;
    }

    .extractor-section {
      flex: 1;
      min-width: 0;
      border-left: 1px solid #e4e7ed;
      padding-left: 20px;
    }
  }

  .form-section {
    flex: 1;
  }
}

.form-container {
  display: flex;
  align-items: center;
  column-gap: 20px;
  width: 100%;
}

.form-field-container {
  display: flex;
  align-items: center;
  column-gap: 10px;
  .modified-field {
    color: red;
    ::v-deep .el-input__inner,
    ::v-deep .el-textarea__inner {
      border-color: #f56c6c !important;
    }

    ::v-deep .el-select .el-input__inner {
      border-color: #f56c6c !important;
    }
  }

  .original-value {
    display: block;
    flex-shrink: 0;
    font-size: 12px;
    color: #909399;

    .original-text {
      cursor: pointer;
    }
  }

  .editing-field {
    ::v-deep .el-textarea__inner {
      border-color: #409eff !important;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
    }
  }
}

.reject-reason-title {
  line-height: 24px;
  font-size: 18px;
  color: #303133;
  .required {
    color: #ff6955;
  }
}

@media (max-width: 1200px) {
  .dialog-container .edit-review-rule-dialog {
    ::v-deep .el-dialog {
      width: 95vw !important;
      max-width: none !important;
    }
  }
}

@media (max-width: 768px) {
  .dialog-container .edit-review-rule-dialog {
    ::v-deep .el-dialog {
      width: 100vw !important;
      height: 100vh !important;
      max-height: none !important;
      border-radius: 0;
    }
  }

  .form-container {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
}
</style>
<style lang="scss">
.original-value-tip {
  &.el-tooltip__popper {
    max-width: 400px;
  }
}
</style>
