{"name": "scriber", "version": "0.9.0", "author": "wushang", "private": true, "scripts": {"serve": "node --max_old_space_size=4096 node_modules/.bin/vue-cli-service serve", "dev": "vue-cli-service serve", "dev-hkex": "node --max_old_space_size=4096 node_modules/.bin/vue-cli-service serve --env hkex", "dev-szse": "vue-cli-service serve --env szse", "dev-szseldap": "vue-cli-service serve --env szseldap", "dev-szseannual": "vue-cli-service serve --env szseannual", "dev-csc": "vue-cli-service serve --env csc", "dev-csc_octopus": "vue-cli-service serve --env csc_octopus", "dev-sse": "vue-cli-service serve --env sse", "dev-ecitic": "vue-cli-service serve --env ecitic", "dev-stencil": "vue-cli-service serve --env stencil", "dev-ccxi": "vue-cli-service serve --env ccxi", "dev-ccxi_contract": "vue-cli-service serve --env ccxi_contract", "dev-ht": "vue-cli-service serve --env ht", "dev-cgs": "vue-cli-service serve --env cgs", "dev-gffund": "vue-cli-service serve --env gffund", "dev-cms": "vue-cli-service serve --env cms", "dev-cmfchina": "vue-cli-service serve --env cmfchina", "dev-chinaamc": "vue-cli-service serve --env chinaamc", "dev-chinaamc_yx": "vue-cli-service serve --env chinaamc_yx", "dev-stronghold": "vue-cli-service serve --env stronghold", "dev-citics_tg": "vue-cli-service serve --env citics_tg", "dev-citics_dcm": "vue-cli-service serve --env citics_dcm", "dev-nafmii": "vue-cli-service serve --env nafmii", "dev-fullgoal": "vue-cli-service serve --env fullgoal", "dev-cmbchina": "vue-cli-service serve --env cmbchina", "dev-zts": "vue-cli-service serve --env zts", "build": "vue-cli-service build", "build-hkex": "vue-cli-service build --env hkex", "build-szse": "vue-cli-service build --env szse", "build-szseldap": "vue-cli-service build --env szseldap", "build-szseannual": "vue-cli-service build --env szseannual", "build-csc": "vue-cli-service build --env csc", "build-csc_octopus": "vue-cli-service build --env csc_octopus", "build-sse": "vue-cli-service build --env sse", "build-ecitic": "vue-cli-service build --env ecitic", "build-stencil": "vue-cli-service build --env stencil", "build-ccxi": "vue-cli-service build --env ccxi", "build-ccxi_contract": "vue-cli-service build --env ccxi_contract", "build-ht": "vue-cli-service build --env ht", "build-cgs": "vue-cli-service build --env cgs", "build-gffund": "vue-cli-service build --env gffund", "build-cms": "vue-cli-service build --env cms", "build-cmfchina": "vue-cli-service build --env cmfchina", "build-chinaamc": "vue-cli-service build --env chinaamc", "build-chinaamc_yx": "vue-cli-service build --env chinaamc_yx", "build-stronghold": "vue-cli-service build --env stronghold", "build-citics_tg": "vue-cli-service build --env citics_tg", "build-citics_dcm": "vue-cli-service build --env citics_dcm", "build-nafmii": "vue-cli-service build --env nafmii", "build-fullgoal": "vue-cli-service build --env fullgoal", "build-cmbchina": "vue-cli-service build --env cmbchina", "build-zts": "vue-cli-service build --env zts", "lint:eslint": "eslint --fix \"src/**/*.{js,vue}\"", "lint:prettier": "prettier --write \"src/**/*.{js,vue,css,scss}\"", "prepare": "husky install"}, "dependencies": {"@fortawesome/fontawesome-free-webfonts": "^1.0.9", "@paoding-label/vue-image-viewer": "0.4.0-alapha.49", "@paoding/prediction-tools": "^0.4.14", "@paoding/prettier-config": "^1.0.1", "@wchbrad/vue-easy-tree": "^1.0.13", "axios": "^0.27.2", "blueimp-md5": "^2.10.0", "chrono-node": "^1.3.5", "core-js": "^3.8.3", "cryptjs": "^2.0.4", "dayjs": "^1.11.8", "echarts": "^4.2.0-rc.1", "element-ui": "^2.15.13", "js-base64": "^3.7.7", "katex": "^0.15.3", "lodash": "^4.17.21", "pdf-document-viewer": "^0.9.103", "qs": "^6.10.3", "uuid": "^3.3.3", "vue": "2.6.14", "vue-i18n": "^8.1.0", "vue-kindergarten": "^0.3.3", "vue-pdf-viewer-next": "npm:vue-pdf-viewer@2.2.9", "vue-router": "^3.0.1", "vuedraggable": "^2.23.2", "vuex": "^3.0.1", "x-data-spreadsheet": "^1.1.9", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.12.16", "@paoding/fe-version-plugin": "^0.1.1", "@paoding/pdf-document-viewer-polyfill": "^0.1.0", "@paoding/vue-cli-env-adaptor": "^0.1.0", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-service": "~5.0.8", "babel-core": "^6.26.3", "babel-preset-es2015": "^6.24.1", "chai": "^4.2.0", "copy-webpack-plugin": "^4.5.1", "eslint": "^8.38.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-vue": "^9.11.0", "git-revision-webpack-plugin": "^5.0.0", "html-minifier": "^4.0.0", "husky": "^8.0.3", "less": "3.0.4", "less-loader": "5.0.0", "lint-staged": "^13.2.1", "mocha": "^5.2.0", "prettier": "^2.8.7", "sass": "^1.49.0", "sass-loader": "^13.3.1", "unsupported-browser-plugin": "0.2.4", "url-loader": "^4.1.1", "vue-cli-plugin-webpack-bundle-analyzer": "^4.0.0", "vue-template-compiler": "2.6.14", "webfonts-loader": "^8.0.1"}, "resolutions": {"lodash": "^4.17.21"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie < 11"], "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "prettier --write"], "src/**/*.{css,scss}": ["prettier --write"]}}